
**Task: Android App Phase 7 - Add Location Fallback via Notification Trigger**

**Objective:**
Add a fallback mechanism for location tracking by checking location sync status whenever a notification is captured. If location hasn't been synced within the expected interval, trigger an immediate location capture.

**Requirements:**
1. **Keep ALL existing functionality** - all previous phases remain intact
2. **Location sync monitoring** - track when locations were last synced to Firebase
3. **Notification-triggered location check** - verify location sync status on every notification
4. **Fallback location capture** - immediately get location if sync is overdue
5. **Enhanced logging** for location fallback diagnostics

**Problem Analysis:**
- Notifications sync successfully to Firebase
- Location sync may fail due to Android OS restrictions, battery optimization, or WorkManager issues
- Use working notification capture as a heartbeat to check location sync health

**Implementation Logic:**
```
On every notification capture:
1. Check timestamp of last location synced to Firebase
2. Calculate time difference from now
3. Get expected interval from Remote Config (default 15 minutes)
4. If time difference > (expected interval + 5 minutes buffer):
   - Log "Location sync overdue"
   - Trigger immediate location capture
   - Sync location to Firebase
   - Log fallback action
```

**Technical Specifications:**
- Modify NotificationListenerService to include location sync checking
- Query Firebase for last location timestamp per device
- Compare against Remote Config location interval + buffer time
- Trigger immediate location capture when overdue
- Use same location capture mechanism as FCM instant location

**Enhanced Logging Format:**
```
[NOTIFICATION] 2024-01-15 14:30:25 | WhatsApp | John Doe | New message | SYNCED
[LOCATION-CHECK] 2024-01-15 14:30:26 | Last location: 25 mins ago | Expected: 15 mins | OVERDUE
[LOCATION-FALLBACK] 2024-01-15 14:30:30 | Triggered by notification | Lat: -6.2088, Lng: 106.8456 | SYNCED
[LOCATION] 2024-01-15 14:45:00 | Lat: -6.2090, Lng: 106.8460 | Accuracy: 12.5m | SYNCED (scheduled)
[NOTIFICATION] 2024-01-15 15:00:25 | Gmail | Google | Email | SYNCED  
[LOCATION-CHECK] 2024-01-15 15:00:26 | Last location: 14 mins ago | Expected: 15 mins | OK
```

**Implementation Requirements:**
1. Last location timestamp tracking (query Firebase or local cache)
2. Location sync health checker
3. Notification-triggered location verification
4. Immediate location capture for fallback scenarios
5. Buffer time calculation (interval + 5 minutes)

**Additional Components Needed:**
1. LocationSyncChecker utility class
2. Last location timestamp cache/query mechanism
3. Overdue detection logic
4. Integration with existing NotificationListenerService
5. Fallback location capture (reuse FCM instant location logic)

**Configuration:**
- Use existing Remote Config `location_interval_minutes`
- Add 5-minute buffer to avoid false positives
- Cache last location timestamp to avoid excessive Firebase queries

**Important Notes:**
- Don't break existing WorkManager location tracking
- Reuse existing location capture mechanisms
- Handle Firebase query failures gracefully
- Only trigger fallback if significantly overdue (buffer time)
- Log all fallback actions for debugging
- Consider rate limiting to avoid excessive location requests

**Edge Cases to Handle:**
- First app run (no previous location data)
- Firebase connectivity issues during timestamp check
- Multiple rapid notifications (avoid duplicate fallback triggers)
- Location permission issues during fallback

**Deliverables:**
1. Updated NotificationListenerService with location sync checking
2. Location fallback mechanism triggered by notifications
3. Last location timestamp tracking and verification
4. Enhanced logging for location sync diagnostics
5. Buffer time logic to prevent false triggers
6. Integration with existing location capture methods
