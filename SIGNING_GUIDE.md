# Android Release Signing Guide

This guide will help you set up release signing for your NotifSyncLite Android app and generate a signed APK for distribution.

## Prerequisites

- Java Development Kit (JDK) installed
- Android Studio or command line tools
- Your project properly configured

## Step 1: Generate a Keystore

### Option A: Using Android Studio (Recommended)
1. Open Android Studio
2. Go to **Build** → **Generate Signed Bundle / APK**
3. Select **APK** and click **Next**
4. Click **Create new...** to create a new keystore
5. Fill in the keystore information:
   - **Key store path**: Choose location (e.g., `release-keystore.jks`)
   - **Password**: Strong password for keystore
   - **Key alias**: Name for your key (e.g., `notifsynclite`)
   - **Key password**: Strong password for the key
   - **Certificate information**: Fill in your details
6. Click **OK** to create the keystore

### Option B: Using Command Line
```bash
# Navigate to your project directory
cd /path/to/NotifSyncLite

# Generate keystore (replace values with your own)
keytool -genkey -v -keystore release-keystore.jks \
    -keyalg RSA -keysize 2048 -validity 10000 \
    -alias notifsynclite \
    -dname "CN=Your Name, OU=Your Organization, O=Your Company, L=Your City, ST=Your State, C=Your Country"

# You'll be prompted for:
# - Keystore password
# - Key password
```

## Step 2: Configure Keystore Properties

1. Copy the sample properties file:
   ```bash
   cp keystore.properties.sample keystore.properties
   ```

2. Edit `keystore.properties` with your actual values:
   ```properties
   storeFile=release-keystore.jks
   storePassword=your_actual_keystore_password
   keyAlias=notifsynclite
   keyPassword=your_actual_key_password
   ```

3. **IMPORTANT**: Add `keystore.properties` to `.gitignore` to keep credentials secure:
   ```bash
   echo "keystore.properties" >> .gitignore
   echo "*.jks" >> .gitignore
   ```

## Step 3: Generate Signed APK

### Option A: Using Android Studio
1. Go to **Build** → **Generate Signed Bundle / APK**
2. Select **APK** and click **Next**
3. Choose your existing keystore file
4. Enter keystore and key passwords
5. Select **release** build variant
6. Choose destination folder
7. Click **Finish**

### Option B: Using Command Line
```bash
# Clean and build release APK
./gradlew clean assembleRelease

# The signed APK will be generated at:
# app/build/outputs/apk/release/app-release.apk
```

## Step 4: Verify Your Signed APK

```bash
# Verify the APK is properly signed
jarsigner -verify -verbose -certs app/build/outputs/apk/release/app-release.apk

# Check APK information
aapt dump badging app/build/outputs/apk/release/app-release.apk
```

## Step 5: Install and Test

```bash
# Install the release APK on your device
adb install app/build/outputs/apk/release/app-release.apk

# Or install with replacement
adb install -r app/build/outputs/apk/release/app-release.apk
```

## Security Best Practices

1. **Keep your keystore safe**: Back it up securely - you cannot recover it if lost
2. **Use strong passwords**: Both keystore and key passwords should be strong
3. **Never commit credentials**: Keep `keystore.properties` and `*.jks` files out of version control
4. **Store keystore securely**: Consider using a password manager or secure storage
5. **Document your credentials**: Keep a secure record of passwords and alias names

## Troubleshooting

### Common Issues:

1. **"keystore.properties not found"**
   - Ensure the file exists in your project root
   - Check the file path in your build.gradle.kts

2. **"Keystore was tampered with, or password was incorrect"**
   - Verify your keystore password in keystore.properties
   - Ensure the keystore file path is correct

3. **"Cannot recover key"**
   - Check your key password in keystore.properties
   - Verify the key alias name

4. **Build fails with signing errors**
   - Ensure all properties in keystore.properties are correct
   - Check that the keystore file exists at the specified path

## Build Variants

Your app now has two build variants:
- **Debug**: `com.ab.notif.debug` - for development and testing
- **Release**: `com.ab.notif` - signed version for distribution

## Next Steps

After generating your signed APK:
1. Test thoroughly on different devices
2. Consider generating an Android App Bundle (AAB) for Play Store
3. Set up automated signing for CI/CD if needed
4. Plan your app versioning strategy

## Generating Android App Bundle (AAB)

For Play Store distribution, consider using AAB instead of APK:

```bash
# Generate signed AAB
./gradlew bundleRelease

# Output location:
# app/build/outputs/bundle/release/app-release.aab
```

The AAB format is preferred by Google Play Store as it allows for optimized APK generation for different device configurations.
