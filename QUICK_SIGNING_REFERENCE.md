# Quick Signing Reference

## 🚀 Quick Start (First Time Setup)

1. **Generate keystore:**
   ```bash
   keytool -genkey -v -keystore release-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias notifsynclite
   ```

2. **Create keystore.properties:**
   ```bash
   cp keystore.properties.sample keystore.properties
   # Edit with your actual values
   ```

3. **Build signed APK:**
   ```bash
   ./gradlew assembleRelease
   ```

## 📱 Build Commands

| Command | Output | Description |
|---------|--------|-------------|
| `./gradlew assembleDebug` | `app-debug.apk` | Debug build with debug signing |
| `./gradlew assembleRelease` | `app-release.apk` | Release build with your signing |
| `./gradlew bundleRelease` | `app-release.aab` | Release bundle for Play Store |

## 📁 Output Locations

- **Debug APK**: `app/build/outputs/apk/debug/app-debug.apk`
- **Release APK**: `app/build/outputs/apk/release/app-release.apk`
- **Release AAB**: `app/build/outputs/bundle/release/app-release.aab`

## 🔧 Verification Commands

```bash
# Verify APK signature
jarsigner -verify -verbose -certs app/build/outputs/apk/release/app-release.apk

# Check APK info
aapt dump badging app/build/outputs/apk/release/app-release.apk

# Install on device
adb install -r app/build/outputs/apk/release/app-release.apk
```

## ⚠️ Important Notes

- **Never commit** `keystore.properties` or `*.jks` files
- **Backup your keystore** - you cannot recover it if lost
- **Test thoroughly** before distributing
- Use **AAB format** for Play Store uploads

## 🆔 App IDs

- **Debug**: `com.ab.notif.debug`
- **Release**: `com.ab.notif`

Both versions can be installed simultaneously for testing.
